# AWOS System Docker Run Command
# Updated for HLS streaming support and main.py entry point

docker run -d --name hlswos \
  -v /dev:/dev \
  --privileged \
  --restart unless-stopped \
  -e API_BASE_URL=https://awosnew.skytraces.com \
  -e STATION=4FL5 \
  -e FREQUENCY=122900 \
  -e VOLUME=100 \
  -e SAMPLE_RATE=48000 \
  -e CHANNELS=1 \
  -e CHUNK_SIZE=1024 \
  -e FREQ_MIN_HZ=200 \
  -e FREQ_MAX_HZ=3500 \
  -e SIGNAL_THRESHOLD_HIGH=10 \
  -e SIGNAL_THRESHOLD_LOW=5 \
  -e CLICK_MIN_DURATION=0.15 \
  -e CLICK_MAX_DURATION=0.6 \
  -e CLICK_COOLDOWN=1.0 \
  -e AWOS_CLICK_COUNT=3 \
  -e RADIO_CHECK_CLICK_COUNT=4 \
  -e RECORDING_STORAGE_PATH=/app/recordings \
  -e PRE_ROLL_SECONDS=0.5 \
  -e POST_ROLL_SECONDS=0.5 \
  -e MIN_SEGMENT_DURATION=0.1 \
  -e MAX_SEGMENT_DURATION=60.0 \
  -e MERGE_GAP_THRESHOLD=5.0 \
  -e STATION_TZ=America/New_York \
  -e ENABLE_RECORDING=true \
  -e ENABLE_SIGNAL_DETECTION=true \
  -e ENABLE_API_UPLOAD=true \
  -e GPIO_CHIP='gpiochip4' \
  -e GPIO_PIN=18 \
  -e ENABLE_HLS_STREAMING=true \
  -e HLS_SEGMENT_DURATION=1 \
  -e HLS_PLAYLIST_SIZE=6 \
  -e HLS_OUTPUT_PATH=/app/hls \
  -e HLS_AUDIO_BITRATE=64 \
  -e HLS_AUDIO_CODEC=aac \
  -p 8080:8080 \
ghcr.io/devtomsuys/hlswos:20250801-214705

# Alternative: Run with HLS streaming disabled
# docker run -d --name better-awos-relay \
#   [... same parameters as above ...] \
#   -e ENABLE_HLS_STREAMING=false \
#   ghcr.io/devtomsuys/better-awos-relay:20250729-232840

# Alternative: Run with volume mounts for HLS output
# docker run -d --name better-awos-relay \
#   [... same parameters as above ...] \
#   -v /path/to/hls/output:/app/hls \
#   ghcr.io/devtomsuys/better-awos-relay:20250729-232840

# Testing Commands:
# docker logs -f hlswos                               # View logs
# docker exec -it hlswos /bin/bash                    # Enter container
# docker exec hlswos python /app/hls_diagnostics.py  # Run HLS diagnostics
# docker exec hlswos ls -la /app/hls                  # Check HLS files
# docker exec hlswos cat /app/hls/playlist.m3u8      # View playlist